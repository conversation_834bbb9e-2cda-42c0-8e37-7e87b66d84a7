import { useSelector, useDispatch } from "react-redux";
import { useEffect, useMemo } from "react";
import { RootState } from "@/store";
import { TPairSetting, ETradingStatus } from "@/types/pair";
import { fetchPairSettings } from "@/store/pairSettings.store";

export const usePairSettings = () => {
  const dispatch = useDispatch();
  const {
    pairSettings,
    pairSettingsMap,
    activePairSettings,
    isLoading,
    isInitialized,
    error,
  } = useSelector((state: RootState) => state.pairSettings);

  // Initialize pair settings if not already done
  useEffect(() => {
    if (!isInitialized && !isLoading) {
      dispatch(fetchPairSettings() as any);
    }
  }, [dispatch, isInitialized, isLoading]);

  // Helper function to get pair setting by symbol
  const getPairSetting = (symbol: string): TPairSetting | null => {
    return pairSettingsMap[symbol.toUpperCase()] || null;
  };

  // Helper function to check if a pair is active
  const isPairActive = (symbol: string): boolean => {
    const setting = getPairSetting(symbol);
    return setting?.status === ETradingStatus.ACTIVE;
  };

  // Helper function to get active pairs by quote asset
  const getActivePairsByQuote = (quoteAsset: string): TPairSetting[] => {
    return activePairSettings.filter(
      (setting: TPairSetting) =>
        setting.quoteAsset?.toUpperCase() === quoteAsset.toUpperCase()
    );
  };

  // Helper function to get all quote assets
  const getQuoteAssets = (): string[] => {
    const quoteAssets = new Set<string>();
    activePairSettings.forEach((setting: TPairSetting) => {
      if (setting.quoteAsset) {
        quoteAssets.add(setting.quoteAsset.toUpperCase());
      }
    });
    return Array.from(quoteAssets).sort();
  };

  // Helper function to get pairs with specific base assets
  const getPairsByBaseAssets = (baseAssets: string[]): TPairSetting[] => {
    const baseAssetsUpper = baseAssets.map((asset) => asset.toUpperCase());
    return activePairSettings.filter((setting: TPairSetting) =>
      baseAssetsUpper.includes(setting.baseAsset?.toUpperCase())
    );
  };

  return {
    // State
    pairSettings,
    pairSettingsMap,
    activePairSettings,
    isLoading,
    isInitialized,
    error,

    // Helper functions
    getPairSetting,
    isPairActive,
    getActivePairsByQuote,
    getQuoteAssets,
    getPairsByBaseAssets,

    // Actions
    refetch: () => dispatch(fetchPairSettings() as any),
  };
};

// Hook specifically for getting a single pair setting
export const usePairSetting = (symbol: string) => {
  const { getPairSetting, isInitialized, isLoading } = usePairSettings();

  const pairSetting = useMemo(() => {
    return symbol ? getPairSetting(symbol) : null;
  }, [symbol, getPairSetting]);

  return {
    pairSetting,
    isInitialized,
    isLoading,
  };
};
