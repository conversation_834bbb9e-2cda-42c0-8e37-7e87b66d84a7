import React, { useMemo } from "react";
import { TPairSetting } from "@/types/pair";
import { getPriceStyle } from "@/utils/helper";
import AppNumber from "./AppNumber";
import Link from "next/link";
import { useMultipleTickers } from "@/hooks/useTicker";
import { usePairSettings } from "@/hooks/usePairSettings";

const PairMarketLanding = () => {
  // Use the optimized ticker hook
  const { tickers } = useMultipleTickers();

  // Use centralized pair settings
  const { activePairSettings, isInitialized } = usePairSettings();

  // Get the default pairs (BTCUSDT and ETHUSDT) from the centralized store
  const validPairs = useMemo(() => {
    if (!isInitialized || !activePairSettings.length) return [];

    const defaultPairs = [
      {
        symbol: "BTCUSDT",
        name: "Bitcoin",
      },
      {
        symbol: "ETHUSDT",
        name: "Ethereum",
      },
    ];

    const finalPairs = defaultPairs
      .map((defaultPair) => {
        const foundPair = activePairSettings.find(
          (pair: TPairSetting) =>
            pair.symbol?.toUpperCase() === defaultPair.symbol
        );
        return foundPair ? { ...foundPair, name: defaultPair.name } : null;
      })
      .filter(Boolean) as (TPairSetting & { name: string })[];

    return finalPairs;
  }, [activePairSettings, isInitialized]);

  return (
    <div className="flex flex-col justify-between gap-8">
      {validPairs.map((pair) => {
        const ticker = tickers[pair.symbol];
        const lastPrice = ticker?.lastPrice || "0";
        const priceChangePercent = ticker?.priceChangePercent || "0";
        const priceChange = ticker?.priceChange || "0";
        const isPositive = parseFloat(priceChangePercent) >= 0;

        return (
          <Link
            key={pair.symbol}
            className="flex justify-between gap-16"
            href={`/trade/${pair.symbol}`}
          >
            <div>
              <div className="heading-lg-semibold-32 mb-2">
                {pair.baseAsset}
              </div>
              <div className="text-white-500 text-[20px] font-medium leading-[1.2]">
                {pair.name || pair.symbol}
              </div>
            </div>
            <div>
              <div
                className="heading-lg-semibold-32 mb-2 text-right"
                style={{ color: getPriceStyle(priceChange) }}
              >
                <AppNumber
                  value={lastPrice}
                  decimals={pair.pricePrecision}
                  isForUSD
                  isFormatLargeNumber={false}
                />
              </div>
              <div
                className="text-right text-[20px] font-medium leading-[1.2]"
                style={{ color: getPriceStyle(priceChange) }}
              >
                {isPositive ? "+" : ""}
                {parseFloat(priceChangePercent).toFixed(2)}%
              </div>
            </div>
          </Link>
        );
      })}
    </div>
  );
};

export default PairMarketLanding;
