"use client";

import { memo, useEffect, useState, useMemo } from "react";
import { SearchIcon, StarIcon, SwapIcon } from "@/assets/icons";
import { AppButtonSort } from "@/components/AppButtonSort";
import rf from "@/services/RequestFactory";
import { TPairMarket, TPairSetting } from "@/types/pair";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/store";
import { useParams, useRouter } from "next/navigation";
import { MarketPairRow } from "./components/MarketPairRow";
import {
  toggleFavoritePair,
  loadFavoritesFromStorage,
} from "@/store/favorites.store";
import { useMultipleTickers } from "@/hooks/useTicker";
import { usePairSettings } from "@/hooks/usePairSettings";

const TAB_KEYS = {
  FAVOURITE: "FAVOURITE",
};

const TABS = [
  {
    name: <StarIcon />,
    value: TAB_KEYS.FAVOURITE,
  },
];

export const MarketExplorer = memo(() => {
  const dispatch = useDispatch();
  const router = useRouter();
  const params = useParams();
  const symbol = params?.symbol ? String(params.symbol) : "";

  // UI state
  const [search, setSearch] = useState<string>("");
  const [tabActive, setTabActive] = useState<string>("USDT");
  const [isShow24hChange, setIsShow24hChange] = useState<boolean>(true);
  const [sortBy, setSortBy] = useState<string>("");
  const [sortType, setSortType] = useState<string>("");

  // Data state
  const [pairMarkets, setPairMarkets] = useState<TPairMarket[]>([]);

  // Use the optimized ticker hook
  const { tickers, renderCount } = useMultipleTickers();

  // Use centralized pair settings
  const { activePairSettings, isInitialized } = usePairSettings();

  const { favoritePairs } = useSelector((state: RootState) => state.favorites);
  const userBalances = useSelector(
    (state: RootState) => state.account.account?.balances || []
  );

  useEffect(() => {
    dispatch(loadFavoritesFromStorage());
  }, [dispatch]);

  const toggleFavorite = (symbol: string) => {
    dispatch(toggleFavoritePair(symbol));
  };

  useEffect(() => {
    const initPairMarket = async () => {
      try {
        const data = await rf.getRequest("PairRequest").getPairMarket();
        setPairMarkets(
          data?.data.map((item: TPairMarket) => ({ ...item, value: item.name }))
        );
      } catch (error) {
        console.log("init pair market error", error);
      }
    };

    initPairMarket();
  }, []);

  // Optimized filtering and sorting with memoization
  const pairSettingsFiltered = useMemo(() => {
    if (!activePairSettings.length) return [];

    let filtered = [...activePairSettings];

    if (tabActive === TAB_KEYS.FAVOURITE) {
      filtered = filtered.filter((pair) => favoritePairs.includes(pair.symbol));
    } else {
      filtered = filtered.filter((pair) => {
        return pair.quoteAsset?.toUpperCase() === tabActive?.toUpperCase();
      });
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter((pair) =>
        pair.symbol.toLowerCase().includes(searchLower)
      );
    }

    if (sortBy && sortType) {
      // Sort by the selected column
      filtered.sort((a, b) => {
        const multiplier = sortType === "asc" ? 1 : -1;

        // Sort by pair name
        if (sortBy === "pair") {
          return multiplier * a.symbol.localeCompare(b.symbol);
        }

        // Sort by price
        if (sortBy === "price") {
          const priceA = parseFloat(tickers[a.symbol]?.lastPrice || "0");
          const priceB = parseFloat(tickers[b.symbol]?.lastPrice || "0");
          return multiplier * (priceA - priceB);
        }

        // Sort by 24h change
        if (sortBy === "24Change") {
          const changeA = parseFloat(
            tickers[a.symbol]?.priceChangePercent || "0"
          );
          const changeB = parseFloat(
            tickers[b.symbol]?.priceChangePercent || "0"
          );
          return multiplier * (changeA - changeB);
        }

        // Sort by volume
        if (sortBy === "Volume") {
          const volumeA = parseFloat(tickers[a.symbol]?.quoteVolume || "0");
          const volumeB = parseFloat(tickers[b.symbol]?.quoteVolume || "0");
          return multiplier * (volumeA - volumeB);
        }

        return 0;
      });
    }

    return filtered;
  }, [
    tabActive,
    search,
    activePairSettings,
    favoritePairs,
    userBalances,
    sortBy,
    sortType,
    tickers,
    renderCount, // Include renderCount to re-run when ticker data changes
  ]);

  const handlePairClick = (newSymbol: string) => {
    if (symbol?.toLowerCase() === newSymbol?.toLowerCase()) {
      return;
    }

    router.replace(`/trade/${newSymbol}`);
  };

  return (
    <div className="border-white-100 h-[420px] border-b p-3">
      <div className="border-white-100 flex items-center gap-2 rounded-[6px] border px-2 py-2">
        <SearchIcon className="text-white-500" />
        <input
          className="body-md-regular-14 placeholder:text-white-300 bg-transparent outline-none"
          placeholder="Search"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      <div className="border-white-50 flex border-b">
        {[...TABS, ...pairMarkets].map((item: TPairMarket, index) => {
          return (
            <div
              onClick={() => setTabActive(item.value)}
              className={`flex h-[40px] cursor-pointer items-center px-3 py-2.5 ${
                item.value === tabActive
                  ? "text-white-1000 body-sm-regular-12 border-white-500 border-b"
                  : "text-white-500 body-sm-regular-12"
              }`}
              key={index}
            >
              {item.name}
            </div>
          );
        })}
      </div>

      <div>
        <div className="flex justify-between">
          <div className="body-sm-regular-12 text-white-500 flex items-center gap-2 p-2">
            Pair{" "}
            <AppButtonSort
              value="pair"
              sortBy={sortBy}
              sortType={sortType}
              setSortType={setSortType}
              setSortBy={setSortBy}
            />
          </div>
          <div className="body-sm-regular-12 text-white-500 flex items-center py-2 text-right">
            <div className="flex items-center gap-2">
              Last price
              <AppButtonSort
                value="price"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
            <div className="ml-1 flex items-center gap-2">
              / {isShow24hChange ? "24h Change" : "Vol"}{" "}
              <AppButtonSort
                value={isShow24hChange ? "24Change" : "Volume"}
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>{" "}
            <SwapIcon
              onClick={() => setIsShow24hChange(!isShow24hChange)}
              className="hover:text-white-1000 ml-0.5 cursor-pointer"
            />
          </div>
        </div>

        <div className="-mx-3">
          {/* Use memoized rows for better performance */}
          {pairSettingsFiltered?.map((item, index) => (
            <MarketPairRow
              key={item.symbol || index}
              item={item}
              ticker={tickers[item.symbol]}
              isShow24hChange={isShow24hChange}
              isFavorite={favoritePairs.includes(item.symbol)}
              onToggleFavorite={toggleFavorite}
              onPairClick={handlePairClick}
            />
          ))}
        </div>
      </div>
    </div>
  );
});

MarketExplorer.displayName = "MarketExplorer";
