"use client";
import { store } from "@/store";
import { ReactNode, useEffect } from "react";
import { Provider } from "react-redux";
import { SessionProvider } from "next-auth/react";
// import { useRouter } from "next/navigation";
import { setUserAuth, clearUser } from "@/store/user.store";
import {
  getNetworks,
  getAssets,
  setSocketConnected,
} from "@/store/metadata.store";
import { fetchPairSettings } from "@/store/pairSettings.store";
import {
  closeSocketInstance,
  createSocketInstance,
  SOCKET_NETWORK,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";

export const AppProvider = ({
  children,
  authorization,
}: {
  children: ReactNode;
  authorization: string;
}) => {
  const handleWhenSocketConnected = () => {
    store.dispatch(setSocketConnected({ socketConnected: true }));

    if (authorization) {
      subscribeSocketChannel({
        params: [""],
        authorization,
      });
    }
  };
  const handleSocketDisconnected = () => {
    store.dispatch(setSocketConnected({ socketConnected: false }));
  };

  useEffect(() => {
    // Initialize app data
    store.dispatch(getNetworks());
    store.dispatch(getAssets());
    store.dispatch(fetchPairSettings());
    createSocketInstance({ network: SOCKET_NETWORK.VDAX });

    AppBroadcast.on(
      BROADCAST_EVENTS.SOCKET_CONNECTED,
      handleWhenSocketConnected
    );
    AppBroadcast.on(
      BROADCAST_EVENTS.SOCKET_DISCONNECTED,
      handleSocketDisconnected
    );

    return () => {
      closeSocketInstance(SOCKET_NETWORK.VDAX);
      AppBroadcast.remove(
        BROADCAST_EVENTS.SOCKET_CONNECTED,
        handleWhenSocketConnected
      );
      AppBroadcast.remove(
        BROADCAST_EVENTS.SOCKET_DISCONNECTED,
        handleSocketDisconnected
      );
    };
  }, []);

  useEffect(() => {
    if (!!authorization) {
      store.dispatch(setUserAuth({ accessToken: authorization }));
      handleWhenSocketConnected();
      return;
    }

    if (!authorization) {
      store.dispatch(clearUser());
      // router.push("/");
    }

    return () =>
      unsubscribeSocketChannel({
        params: [""],
      });
  }, [authorization]);

  return (
    <Provider store={store}>
      <SessionProvider>{children}</SessionProvider>
    </Provider>
  );
};
