import { headers } from "next/headers";
import { PairProvider } from "./provider";
import { Header, Footer } from "@/layouts";

export default async function PairLayout({
  children,
  desktop,
  mobile,
  params,
}: {
  children: React.ReactNode;
  desktop: React.ReactNode;
  mobile: React.ReactNode;
  params: Promise<{ symbol: string }>;
}) {
  // Await params before accessing its properties
  const { symbol } = await params;

  const headersList = await headers();
  const isMobile = (headersList.get("user-agent") || "")?.includes("Mobile");

  return (
    <PairProvider pairSetting={null} symbol={symbol}>
      <div>
        <Header />
        <div className="bg-white-50 min-h-screen pb-0 pt-[50px] md:pb-[50px]">
          {isMobile ? mobile : desktop}
        </div>
        <Footer />
      </div>
    </PairProvider>
  );
}
