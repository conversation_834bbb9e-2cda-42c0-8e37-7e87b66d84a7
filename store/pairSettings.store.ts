import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import { TPairSetting, ETradingStatus } from "@/types/pair";
import rf from "@/services/RequestFactory";

export interface PairSettingsState {
  pairSettings: TPairSetting[];
  pairSettingsMap: { [symbol: string]: TPairSetting };
  activePairSettings: TPairSetting[];
  isLoading: boolean;
  isInitialized: boolean;
  error: string | null;
}

const initialState: PairSettingsState = {
  pairSettings: [],
  pairSettingsMap: {},
  activePairSettings: [],
  isLoading: false,
  isInitialized: false,
  error: null,
};

// Async thunk to fetch all pair settings
export const fetchPairSettings = createAsyncThunk(
  "pairSettings/fetchPairSettings",
  async (_, { rejectWithValue }) => {
    try {
      const response = await rf.getRequest("PairRequest").getTradingPairs();
      return response?.data || [];
    } catch (error: any) {
      console.error("Failed to fetch pair settings:", error);
      return rejectWithValue(error.message || "Failed to fetch pair settings");
    }
  }
);

export const pairSettingsSlice = createSlice({
  name: "pairSettings",
  initialState,
  reducers: {
    setPairSettings: (state, action: PayloadAction<TPairSetting[]>) => {
      state.pairSettings = action.payload;
      
      // Create a map for quick lookups
      state.pairSettingsMap = {};
      action.payload.forEach((setting) => {
        state.pairSettingsMap[setting.symbol.toUpperCase()] = setting;
      });

      // Filter active pairs
      state.activePairSettings = action.payload.filter(
        (setting) => setting.status === ETradingStatus.ACTIVE
      );
      
      state.isInitialized = true;
      state.error = null;
    },
    clearPairSettings: (state) => {
      state.pairSettings = [];
      state.pairSettingsMap = {};
      state.activePairSettings = [];
      state.isInitialized = false;
      state.error = null;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPairSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPairSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.pairSettings = action.payload;
        
        // Create a map for quick lookups
        state.pairSettingsMap = {};
        action.payload.forEach((setting: TPairSetting) => {
          state.pairSettingsMap[setting.symbol.toUpperCase()] = setting;
        });

        // Filter active pairs
        state.activePairSettings = action.payload.filter(
          (setting: TPairSetting) => setting.status === ETradingStatus.ACTIVE
        );
        
        state.isInitialized = true;
        state.error = null;
      })
      .addCase(fetchPairSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setPairSettings, clearPairSettings, setError } = pairSettingsSlice.actions;

export default pairSettingsSlice.reducer;
